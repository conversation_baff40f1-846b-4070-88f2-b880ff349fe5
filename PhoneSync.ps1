# PhoneSync - File Organization Script
# Organizes JPG and MP4 files into date-based folders with Wudan rules
# Author: Generated for PhoneSync Project
# Date: 2025-08-31

param(
    [string]$ConfigPath = "config.json",
    [switch]$DryRun,
    [switch]$Verbose
)

# Global variables
$script:Config = $null
$script:LogFile = $null

# Function to write log messages
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    # Write to console
    switch ($Level) {
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        "WARN"  { Write-Host $logMessage -ForegroundColor Yellow }
        "INFO"  { Write-Host $logMessage -ForegroundColor Green }
        default { Write-Host $logMessage }
    }
    
    # Write to log file if enabled
    if ($script:Config.logging.enabled -and $script:LogFile) {
        Add-Content -Path $script:LogFile -Value $logMessage
    }
}

# Function to load configuration
function Load-Configuration {
    param([string]$ConfigPath)
    
    try {
        if (-not (Test-Path $ConfigPath)) {
            throw "Configuration file not found: $ConfigPath"
        }
        
        $configContent = Get-Content $ConfigPath -Raw | ConvertFrom-Json
        Write-Log "Configuration loaded successfully from $ConfigPath"
        return $configContent
    }
    catch {
        Write-Log "Failed to load configuration: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to initialize logging
function Initialize-Logging {
    if ($script:Config.logging.enabled) {
        $script:LogFile = $script:Config.logging.logPath
        
        # Create log directory if it doesn't exist
        $logDir = Split-Path $script:LogFile -Parent
        if ($logDir -and -not (Test-Path $logDir)) {
            New-Item -ItemType Directory -Path $logDir -Force | Out-Null
        }
        
        # Rotate log if it's too large
        if (Test-Path $script:LogFile) {
            $logSize = (Get-Item $script:LogFile).Length / 1MB
            if ($logSize -gt $script:Config.logging.maxLogSizeMB) {
                $backupLog = $script:LogFile -replace '\.log$', "_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
                Move-Item $script:LogFile $backupLog
                Write-Log "Log rotated to $backupLog"
            }
        }
        
        Write-Log "=== PhoneSync Started ===" "INFO"
    }
}

# Function to check if file matches Wudan time rules
function Test-WudanTimeRules {
    param(
        [DateTime]$FileDate
    )
    
    $year = $FileDate.Year
    $dayOfWeek = [int]$FileDate.DayOfWeek  # Sunday = 0, Monday = 1, etc.
    $timeOfDay = $FileDate.ToString("HH:mm")
    
    # Determine which rule set to use
    $rules = if ($year -lt 2021) { 
        $script:Config.wudanRules.before2021 
    } else { 
        $script:Config.wudanRules.after2021 
    }
    
    # Check if day of week matches
    if ($dayOfWeek -notin $rules.daysOfWeek) {
        return $false
    }
    
    # Check if time falls within any of the time ranges
    foreach ($timeRange in $rules.timeRanges) {
        if ($timeOfDay -ge $timeRange.start -and $timeOfDay -le $timeRange.end) {
            return $true
        }
    }
    
    return $false
}

# Function to get target folder path
function Get-TargetFolderPath {
    param(
        [string]$FilePath,
        [DateTime]$FileDate
    )
    
    $extension = [System.IO.Path]::GetExtension($FilePath).ToLower()
    $dateFolder = $FileDate.ToString("yyyy_MM_dd")
    
    # Determine if it's a picture or video
    if ($extension -in $script:Config.fileExtensions.pictures) {
        return Join-Path $script:Config.targetPaths.pictures $dateFolder
    }
    elseif ($extension -in $script:Config.fileExtensions.videos) {
        # Check Wudan rules for videos
        if (Test-WudanTimeRules -FileDate $FileDate) {
            return Join-Path $script:Config.targetPaths.wudanVideos $dateFolder
        }
        else {
            return Join-Path $script:Config.targetPaths.videos $dateFolder
        }
    }
    else {
        Write-Log "Unsupported file extension: $extension for file $FilePath" "WARN"
        return $null
    }
}

# Function to ensure target directory exists
function Ensure-TargetDirectory {
    param([string]$TargetPath)
    
    if (-not (Test-Path $TargetPath)) {
        if ($script:Config.options.createMissingFolders) {
            try {
                New-Item -ItemType Directory -Path $TargetPath -Force | Out-Null
                Write-Log "Created directory: $TargetPath"
                return $true
            }
            catch {
                Write-Log "Failed to create directory $TargetPath`: $($_.Exception.Message)" "ERROR"
                return $false
            }
        }
        else {
            Write-Log "Target directory does not exist and creation is disabled: $TargetPath" "WARN"
            return $false
        }
    }
    return $true
}

# Function to copy file to target location
function Copy-FileToTarget {
    param(
        [string]$SourcePath,
        [string]$TargetPath,
        [string]$FileName
    )

    $targetFile = Join-Path $TargetPath $FileName

    # Check if file already exists
    if (Test-Path $targetFile) {
        $sourceSize = (Get-Item $SourcePath).Length
        $targetSize = (Get-Item $targetFile).Length

        if ($sourceSize -eq $targetSize) {
            Write-Log "File already exists with same size, skipping: $FileName"
            return $true
        }
        else {
            # Create unique filename
            $baseName = [System.IO.Path]::GetFileNameWithoutExtension($FileName)
            $extension = [System.IO.Path]::GetExtension($FileName)
            $counter = 1

            do {
                $newFileName = "${baseName}_${counter}${extension}"
                $targetFile = Join-Path $TargetPath $newFileName
                $counter++
            } while (Test-Path $targetFile)

            Write-Log "File exists with different size, creating unique name: $newFileName"
        }
    }

    try {
        if ($script:Config.options.copyFiles -and -not $DryRun) {
            Copy-Item -Path $SourcePath -Destination $targetFile -Force
            Write-Log "Copied: $SourcePath -> $targetFile"
        }
        else {
            Write-Log "[DRY RUN] Would copy: $SourcePath -> $targetFile"
        }
        return $true
    }
    catch {
        Write-Log "Failed to copy file $SourcePath to $targetFile`: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Function to process files in a directory
function Process-Directory {
    param([string]$SourcePath)

    Write-Log "Processing directory: $SourcePath"

    if (-not (Test-Path $SourcePath)) {
        Write-Log "Source directory does not exist: $SourcePath" "ERROR"
        return
    }

    # Get all supported files recursively
    $allExtensions = $script:Config.fileExtensions.pictures + $script:Config.fileExtensions.videos
    $files = Get-ChildItem -Path $SourcePath -Recurse -File | Where-Object {
        $_.Extension.ToLower() -in $allExtensions
    }

    Write-Log "Found $($files.Count) files to process"

    $processedCount = 0
    $errorCount = 0

    foreach ($file in $files) {
        try {
            # Get file's last modified date
            $fileDate = $file.LastWriteTime

            # Get target folder path
            $targetFolder = Get-TargetFolderPath -FilePath $file.FullName -FileDate $fileDate

            if ($targetFolder) {
                # Ensure target directory exists
                if (Ensure-TargetDirectory -TargetPath $targetFolder) {
                    # Copy file
                    if (Copy-FileToTarget -SourcePath $file.FullName -TargetPath $targetFolder -FileName $file.Name) {
                        $processedCount++
                    }
                    else {
                        $errorCount++
                    }
                }
                else {
                    $errorCount++
                }
            }
            else {
                $errorCount++
            }

            # Progress reporting
            if ($Verbose -or $script:Config.options.verboseLogging) {
                $progress = [math]::Round(($processedCount + $errorCount) / $files.Count * 100, 1)
                Write-Log "Progress: $progress% ($($processedCount + $errorCount)/$($files.Count))"
            }
        }
        catch {
            Write-Log "Error processing file $($file.FullName): $($_.Exception.Message)" "ERROR"
            $errorCount++
        }
    }

    Write-Log "Directory processing complete. Processed: $processedCount, Errors: $errorCount"
}

# Main execution function
function Main {
    try {
        # Load configuration
        $script:Config = Load-Configuration -ConfigPath $ConfigPath

        # Override config with command line parameters
        if ($DryRun) {
            $script:Config.options.dryRun = $true
        }
        if ($Verbose) {
            $script:Config.options.verboseLogging = $true
        }

        # Initialize logging
        Initialize-Logging

        Write-Log "Starting PhoneSync with configuration from $ConfigPath"

        if ($script:Config.options.dryRun) {
            Write-Log "DRY RUN MODE - No files will be copied" "WARN"
        }

        # Process each source folder
        foreach ($sourceFolder in $script:Config.sourceFolders) {
            Process-Directory -SourcePath $sourceFolder
        }

        Write-Log "=== PhoneSync Completed ===" "INFO"
    }
    catch {
        Write-Log "Fatal error: $($_.Exception.Message)" "ERROR"
        exit 1
    }
}

# Execute main function
Main
